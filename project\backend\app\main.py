from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routers import policy, illustration
from app.routers.illustration import selected_illustrations_routes

app = FastAPI(
    title="In-Force Illustration API",
    description="Backend API to manage insurance policies, search/view policy data, and generate in-force illustrations.",
    version="1.0.0"
)

# Optional: Enable CORS for frontend integration (React or Postman testing)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, set specific domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ✅ Only register once without extra prefixes
app.include_router(policy.router, prefix="/policy")


#  🔹 Illustration APIs (Get, Store, Retrieve)
app.include_router(
    illustration.router,
    prefix="/illustration/policy",
    tags=["Illustration"]
)

#  ✅ Health check endpoint
@app.get("/")
def root():
    return {"message": "In-Force Illustration API is running successfully"}

# Optional: Uvicorn run
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="127.0.0.1", port=8000, reload=True)







