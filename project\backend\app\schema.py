from datetime import date
from typing import Optional,List, Union
from pydantic import BaseModel, Field


class PolicySearchRequest(BaseModel):
    customer_name: Optional[str] = Field("", description="Customer's first name for search")
    policy_id: Optional[int]= Field(None, description="Policy ID for search")
    customer_id: Optional[int] = Field(None, description="Customer ID for search")

# One policy response item
class PolicyBasicDetail(BaseModel):
    policyId: int
    customerId: int
    customer_name: str
    policyType: str
    status: str
    maturityDate: date
    faceAmount: float


# Full success response
class PolicyBasicDetailsResponse(BaseModel):
    POLICY_DETAILS: List[PolicyBasicDetail]

class ErrorResponse(BaseModel):
    errorCode: str
    message:str


#VIEW POLICY RESPONSE
class Rider(BaseModel):
    RIDER_ID: int
    POLICY_ID: int
    RIDER_NAME: str
    SUM_ASSURED: float
    PREMIUM_AMOUNT: float

class Beneficiary(BaseModel):
    BENEFICIARY_ID: int
    POLICY_ID: int
    NAME: str
    RELATIONSHIP: str
    SHARE_PERCENTAGE: float

class Transaction(BaseModel):
    TRANSACTION_ID: int
    POLICY_ID: int
    TRANSACTION_TYPE: str
    TRANSACTION_DATE: str
    AMOUNT: float

class ScheduleRepayment(BaseModel):
    REPAYMENT_ID: int
    LOAN_ID: int
    DUE_DATE: str
    AMOUNT_DUE: float
    STATUS: str

class Loan(BaseModel):
    LOAN_ID: int
    POLICY_ID: int
    LOAN_AMOUNT: float
    INTEREST_RATE: float
    START_DATE: str
    repayments: Optional[List[ScheduleRepayment]] = []

class PolicyViewResponse(BaseModel):
    POLICY_ID: int
    CUSTOMER_ID: int
    INSURANCE_COMPANY_ID: int
    AGENT_ID: int
    PRODUCT_ID: int
    POLICY_NUMBER: str
    POLICY_START_DATE: str
    POLICY_END_DATE: str
    POLICY_STATUS: str
    PREMIUM_AMOUNT: float
    FACE_AMOUNT: float
    riders: Optional[List[Rider]] = []
    beneficiaries: Optional[List[Beneficiary]] = []
    transactions: Optional[List[Transaction]] = []
    loans: Optional[List[Loan]] = []


# Response for Search
class PolicyserachResponse(BaseModel):
    policy_id: int
    customer_id: int
    customer_first_name: str
    customer_last_name: str
    product_id: int
    date_of_birth: date
    policy_status: Optional[str] = None
    face_amount: Optional[float] = None
    maturity_date: Optional[date] = None


class SelectedOptionRequest(BaseModel):
    optionId: int  # ✅ single integer
    value: Union[str, int, float, None]

class SelectedIllustrationRequest(BaseModel):
    IllustrationId: Union[int, str]
    selectedIllustrationOptions: List[SelectedOptionRequest]

class SelectedIllustrationOption(BaseModel):
    scenarioId: int
    value: str

class StoreIllustrationOptionsRequest(BaseModel):
    policyId: str
    illustrationTypeId: int
    selectedIllustrationOptions: List[SelectedIllustrationOption]

class ScenarioSaveStatus(BaseModel):
    scenarioId: int
    status: str

class StoreIllustrationOptionsResponse(BaseModel):
    policyId: str
    illustrationId: int
    optionsSaved: List[ScenarioSaveStatus]

