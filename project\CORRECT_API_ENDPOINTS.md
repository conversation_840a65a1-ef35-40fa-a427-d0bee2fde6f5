# Correct API Endpoints for PolicySelection Component

## ✅ Fixed: Now Using Proper HTTP Methods

You were absolutely right! For retrieving data, we should use **GET requests**, not POST. Here are the corrected endpoints:

## 🔥 Required Backend Endpoints

### 1. Search Endpoint (GET)
**URL:** `GET /api/search`

**Query Parameters:**
```
?customerId=CUS-123456&policyNumber=POL-789012&customerName=<PERSON>
```

**Example Request:**
```
GET /api/search?customerId=CUS-123456&policyNumber=POL-789012&customerName=John%20Smith
```

**Response:**
```json
{
  "success": true,
  "data": {
    "DOB": "05.02.1994",
    "Email": "<EMAIL>",
    "Phone": "(*************",
    "Address": "123 Main Street, New York, NY 10001",
    "Occupation": "Software Engineer",
    "Annual Income": "$85,000",
    "Customer ID": "CUS-123456",
    "Policy Number": "POL-789012",
    "Policy Type": "Whole Life Insurance",
    "Status": "Active",
    "Available Policies": [
      {
        "id": "POL-789012",
        "name": "Whole Life Insurance",
        "description": "Permanent life insurance with cash value",
        "coverage": "500,000 $",
        "premium": "2000 $ annually",
        "features": ["Cash Value Growth", "Tax Benefits", "Loan Options"]
      }
    ]
  }
}
```

### 2. View Endpoint (GET)
**URL:** `GET /api/view`

**Query Parameters:**
```
?policyId=POL-789012&customerId=CUS-123456
```

**Example Request:**
```
GET /api/view?policyId=POL-789012&customerId=CUS-123456
```

**Response:**
```json
{
  "success": true,
  "data": {
    "policy": {
      "id": "POL-789012",
      "name": "Whole Life Insurance",
      "description": "Permanent life insurance with cash value",
      "coverage": "500,000 $",
      "premium": "2000 $ annually",
      "features": ["Cash Value Growth", "Tax Benefits"],
      "status": "Active",
      "issueDate": "01/01/2024",
      "nextDueDate": "01/01/2025",
      "cashValue": "$25,000",
      "paymentFrequency": "Annually"
    },
    "paymentHistory": [
      {
        "date": "01/15/2024",
        "amount": "$2000",
        "status": "Paid"
      }
    ],
    "transactionHistory": [
      {
        "id": "TNX001",
        "date": "03-01-2024",
        "type": "Premium Payment",
        "amount": "2000.00",
        "remarks": "Annual premium payment"
      }
    ],
    "riders": [
      {
        "name": "Critical Illness Rider",
        "coverage": "$50,000",
        "status": "Active"
      }
    ]
  }
}
```

## 🚀 How It Works in the Code

### Search API Call:
```typescript
// 🔥 BACKEND ENDPOINT 1: SEARCH API
// 📍 YOUR BACKEND NEEDS TO IMPLEMENT: GET /api/search
const searchCustomerPolicies = async (customerId, policyNumber, customerName) => {
  // 🚀 BACKEND REQUEST: GET request with query parameters
  const params = new URLSearchParams({
    customerId: customerId.trim(),
    policyNumber: policyNumber.trim(),
    customerName: customerName.trim(),
  });

  const response = await fetch(`${API_BASE_URL}/search?${params}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  // 📊 BACKEND RESPONSE: Customer data with available policies
  const data = await response.json();
  return data;
};
```

### View API Call:
```typescript
// 🔥 BACKEND ENDPOINT 2: VIEW API  
// 📍 YOUR BACKEND NEEDS TO IMPLEMENT: GET /api/view
const getCompleteCustomerInfo = async (policyId, customerId) => {
  // 🚀 BACKEND REQUEST: GET request with query parameters
  const params = new URLSearchParams({
    policyId: policyId,
    customerId: customerId,
  });

  const response = await fetch(`${API_BASE_URL}/view?${params}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  // 📊 BACKEND RESPONSE: Complete customer information
  const data = await response.json();
  return data;
};
```

## 🎯 Summary

✅ **Fixed Issues:**
- Changed from POST to GET requests for data retrieval
- Using query parameters instead of request body
- Proper HTTP methods for RESTful API design

✅ **What You Need to Implement:**
1. `GET /api/search` - with query parameters for customer search
2. `GET /api/view` - with query parameters for policy details

✅ **Benefits:**
- Proper REST API design
- Cacheable GET requests
- Cleaner URL structure
- Standard HTTP methods

The component is now using the correct HTTP methods for data retrieval!
