from http.client import HTT<PERSON><PERSON>x<PERSON>
import json
from typing import Any, Dict, List
from app.database import get_main_connection

def get_full_illustration_options(policy_id: int) -> Dict[str, Any]:
    conn = get_main_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        cursor.execute("""
           SELECT 
            t.ILLUSTRATION_TYPE_ID,
            t.DESCRIPTION AS Type_Description,

            q.ILLUSTRATION_QUESTION_ID,
            q.DESCRIPTION AS Question_Description,

            v.ILLUSTRATION_VARIANT_ID,
            v.DESCRIPTION AS Variant_Description,

            o.ILLUSTRATION_OPTION_ID,
            o.ILLUSTRATION_OPTION_TYPE,
            o.DESCRIPTION AS Option_Description
        FROM ILLUSTRATION_TYPE t
        JOIN ILLUSTRATION_QUESTION q ON q.ILLUSTRATION_TYPE_ID = t.ILLUSTRATION_TYPE_ID
        JOIN ILLUSTRATION_VARIANT v ON v.ILLUSTRATION_QUESTION_ID = q.ILLUSTRATION_QUESTION_ID
        JOIN ILLUSTRATION_VARIANT_OPTION o 
            ON o.ILLUSTRATION_VARIANT_ID = v.ILLUSTRATION_VARIANT_ID
            AND o.ILLUSTRATION_TYPE_ID = t.ILLUSTRATION_TYPE_ID
            AND o.ILLUSTRATION_QUESTION_ID = q.ILLUSTRATION_QUESTION_ID
        ORDER BY 
            t.ILLUSTRATION_TYPE_ID, 
            q.ILLUSTRATION_QUESTION_ID, 
            v.ILLUSTRATION_VARIANT_ID, 
            o.ILLUSTRATION_OPTION_ID
    """)

        rows = cursor.fetchall()
        if not rows:
            raise HTTPException(status_code=404, detail="No illustration data found")

        tree = {}
        for row in rows:
            type_id = row["ILLUSTRATION_TYPE_ID"]
            q_id = row["ILLUSTRATION_QUESTION_ID"]
            v_id = row["ILLUSTRATION_VARIANT_ID"]
            o_id = row["ILLUSTRATION_OPTION_ID"]

            if type_id not in tree:
                tree[type_id] = {
                    "type_id": type_id,
                    "type_description": row["Type_Description"],
                    "questions": {}
                }

            if q_id not in tree[type_id]["questions"]:
                tree[type_id]["questions"][q_id] = {
                    "question_id": q_id,
                    "question_description": row["Question_Description"],
                    "variants": {}
                }

            if v_id not in tree[type_id]["questions"][q_id]["variants"]:
                tree[type_id]["questions"][q_id]["variants"][v_id] = {
                    "variant_id": v_id,
                    "variant_description": row["Variant_Description"],
                    "options": []
                }

            # Build the option with selection info and conditional options
            option_entry = {
                "option_id": o_id,
                "option_description": row["Option_Description"],
                "option_type": row["ILLUSTRATION_OPTION_TYPE"],
                "selected": False,
                "conditional_options": []
            }

            # Add conditional options if any are selected
            for idx in range(1, 5):
                cond_id = row.get(f"Conditional_Option_ID{idx}")
                if cond_id:
                    label = store_options_by_scenario(cond_id)
                    option_entry["conditional_options"].append({
                        "option_id": cond_id,
                        "description": label["Option_Text"]
                    })

            tree[type_id]["questions"][q_id]["variants"][v_id]["options"].append(option_entry)

        # Convert nested dicts to lists
        # Filter function to be added later
        result = {
            "policy_id": policy_id,
            "illustration_options": [
                {
                    "type_id": t["type_id"],
                    "type_description": t["type_description"],
                    "questions": [
                        {
                            "question_id": q["question_id"],
                            "question_description": q["question_description"],
                            "variants": [
                                {
                                    "variant_id": v["variant_id"],
                                    "variant_description": v["variant_description"],
                                    "options": v["options"]
                                }
                                for v in q["variants"].values()
                            ]
                        }
                        for q in t["questions"].values()
                    ]
                }
                for t in tree.values()
            ]
        }

        return result

    finally:
        cursor.close()
        conn.close()



def store_options_by_scenario(policy_id: str, selected_options: List[dict]):
    conn = get_main_connection()
    cursor = conn.cursor(buffered=True)

    # Step 1: Get latest illustration_id for the given policy_id
    cursor.execute("""
        SELECT ILLUSTRATION_ID 
        FROM ILLUSTRATION_TABLE
        WHERE POLICY_ID = %s
        ORDER BY ILLUSTRATION_DATE DESC 
        LIMIT 1
    """, (policy_id,))
    row = cursor.fetchone()

    if not row:
        raise Exception(f"No illustration found for policy ID: {policy_id}")

    illustration_id = row[0]
    options_saved = []

    # Step 2: Loop through selected options
    for opt in selected_options:
        scenario_id = opt["scenarioId"]
        value = opt["value"]
        query = """
            SELECT 
            s.SCENARIO_ID,
            i.POLICY_ID,
            i.ILLUSTRATION_ID,
            sch.SCHEDULE_ID,
            s.ILLUSTRATION_TYPE_ID,
            s.ILLUSTRATION_QUESTION_ID,
            s.ILLUSTRATION_VARIANT_ID,
            s.ILLUSTRATION_OPTION_ID,
            s.CONDITIONAL_OPTION_ID1,
            s.CONDITIONAL_OPTION_ID2,
            s.CONDITIONAL_OPTION_ID3,
            s.CONDITIONAL_OPTION_ID4
            FROM 
            STORED_ILLUSTRATION_OPTION s
            JOIN 
                ILLUSTRATION_TABLE i 
                ON i.POLICY_ID = %s
            JOIN 
                SCHEDULE_TABLE sch 
                ON sch.ILLUSTRATION_ID = i.ILLUSTRATION_ID
            WHERE 
                s.SCENARIO_ID = %s
                AND i.POLICY_ID = %s
            """

# Notice: policy_id is used twice, so it's passed twice
        cursor.execute(query, (policy_id, scenario_id, policy_id))

        data = cursor.fetchone()

        if not data:
            options_saved.append({
                "scenarioId": scenario_id,
                "status": "not found in join"
            })
            continue

        (
            scenario_id_fetched,
            policy_id_fetched,
            illustration_id_fetched,
            schedule_id,
            type_id,
            question_id,
            variant_id,
            option_id,
            cond1,
            cond2,
            cond3,
            cond4
        ) = data

        # Step 4: Insert into SELECTED_ILLUSTRATION_OPTIONS
        cursor.execute("""
            INSERT INTO SELECTED_ILLUSTRATION_OPTION (
                SCENARIO_ID,
                POLICY_ID,
                ILLUSTRATION_ID,
                SCHEDULE_ID,
                ILLUSTRATION_TYPE_ID,
                ILLUSTRATION_QUESTION_ID,
                ILLUSTRATION_VARIANT_ID,
                ILLUSTRATION_OPTION_ID,
                ILLUSTRATION_CONDITIONAL_OPTION_ID1,
                ILLUSTRATION_CONDITIONAL_OPTION_ID2,
                ILLUSTRATION_CONDITIONAL_OPTION_ID3,
                ILLUSTRATION_CONDITIONAL_OPTION_ID4
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            scenario_id_fetched, policy_id_fetched, illustration_id_fetched, schedule_id,
            type_id, question_id, variant_id, option_id,
            cond1, cond2, cond3, cond4
        ))

        options_saved.append({"scenarioId": scenario_id, "status": "saved"})

    conn.commit()
    cursor.close()
    conn.close()

    return {
        "policyId": policy_id,
        "illustrationId": illustration_id,
        "optionsSaved": options_saved
    }




