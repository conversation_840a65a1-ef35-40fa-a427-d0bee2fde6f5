import mysql.connector
from mysql.connector import Error

# 🔹 Connection for Main DB (e.g., 'insurance')
def get_main_connection():
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456789@abc',
            database='INSURANCE'
        )
        if connection.is_connected():
            print("[MAIN DB] MySQL connection successful!")
            return connection
    except Error as e:
        print(f"[MAIN DB] Error connecting to MySQL: {e}")
        return None
# 🔹 Optional: Test both connections when run directly
if __name__ == "__main__":
    print("🔸 Testing Main DB:")
    main_conn = get_main_connection()
    if main_conn:
        cursor = main_conn.cursor()
        cursor.execute("SHOW TABLES")
        print("Main DB Tables:", cursor.fetchall())
        cursor.close()
        main_conn.close()

    print("\n🔸 Testing Dummy DB:")
    dummy_conn = get_dummy_connection()
    if dummy_conn:
        cursor = dummy_conn.cursor()
        cursor.execute("SHOW TABLES")
        print("Dummy DB Tables:", cursor.fetchall())
        cursor.close()
        dummy_conn.close()
