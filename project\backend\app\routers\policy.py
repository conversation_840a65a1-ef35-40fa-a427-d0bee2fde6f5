from fastapi import APIRouter, HTTPException, Body, Query
from fastapi.responses import JSONResponse
from typing import Any, Dict, List, Union
import requests
import httpx

from app.schema import (
    PolicySearchRequest,
    PolicyserachResponse,
    PolicyViewResponse,
    PolicyBasicDetail,
    PolicyBasicDetailsResponse,
    ErrorResponse,
)
from app.services import policyservices
from app.services.policyservices import search_policy_insurer

router = APIRouter(
    prefix="/policy",
    tags=["Policy"]
)


# ✅ 1️⃣ UI initiates this search (calls insurer backend internally)
@router.post("/api/policy/search/ui", response_model=Union[PolicyBasicDetailsResponse, ErrorResponse])
async def search_policy_controller(request: PolicySearchRequest):
    # Remove None/empty/zero values
    filtered_keys = {
        k: v for k, v in request.model_dump().items()
        if v not in (None, "", 0)
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://127.0.0.1:8000/policy/policy/api/policy/search/insurer",
                json=filtered_keys
            )

        if response.status_code == 404:
            return JSONResponse(
                status_code=404,
                content={
                    "errorCode": "POLICY_NOT_FOUND",
                    "message": "No policy found matching the provided details."
                }
            )

        result = response.json()
        return {"POLICY_DETAILS": result}

    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Internal API call failed: {e}")


# ✅ 2️⃣ Insurer backend directly searches DB
@router.post("/api/policy/search/insurer", response_model=List[PolicyBasicDetail])
def customer_policy_search_api(request: PolicySearchRequest):
    search_keys = {
        "policy_id": request.policy_id,
        "customer_id": request.customer_id,
        "customer_name": request.customer_name
    }

    result = search_policy_insurer(search_keys)

    if not result:
        raise HTTPException(status_code=404, detail="No policies found for the given search keys")

    return result


# ✅ 3️⃣ View policy details via insurer (internal fetch)
@router.get("/insurer/view")
def view_policy_insurer(policy_id: int):
    try:
        print(f"🔍 Fetching policy details for ID: {policy_id}")
        policy_data = policyservices.fetch_full_policy_details(policy_id)
        return policy_data
    except Exception as e:
        print("❌ Error fetching policy:", e)
        return JSONResponse(status_code=404, content={"error": str(e)})


# ✅ 4️⃣ Store policy details from UI into ILL tables
@router.post("/ui/viewed_policy")
def store_viewed_policy_ui(policy_id: int = Query(...)):
    try:
        print(f"📦 UI requested to store policy ID {policy_id} in ILL tables")

        # 🔁 Call insurer's GET API to fetch full policy details
        insurer_api_url = f"http://127.0.0.1:8000/policy/policy/insurer/view?policy_id={policy_id}"
        response = requests.get(insurer_api_url)

        if response.status_code != 200:
            print(f"❌ Error calling insurer API: {response.text}")
            return JSONResponse(status_code=response.status_code, content={"error": "Could not fetch policy from insurer"})

        policy_data = response.json()
        print(f"Fetched policy data: {policy_data}")

        # ✅ Now store this JSON in the ILL tables
        policyservices.store_policy_in_ill_tables(policy_data)

        return {"status": "success",
            "message": f"Policy {policy_id} stored in ILL tables.",
            "data": policy_data}

    except Exception as e:
        print("❌ Error during store_viewed_policy_ui:", e)
        return JSONResponse(status_code=500, content={"error": str(e)})








